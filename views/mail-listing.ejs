<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter Testing Tool - Mail Testing</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold">Newsletter Testing Tool</h1>
                </div>
            </div>
        </div>
    </nav>
    
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div>
            <h2 class="text-2xl font-bold mb-6">Mail Testing</h2>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <ul class="divide-y divide-gray-200">
                    <% messages.forEach(message => { %>
                        <li class="px-6 py-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center text-sm text-gray-500 mb-1">
                                        <span class="font-medium text-gray-900"><%= message.from && message.from[0] ? message.from[0].name : 'Unknown Sender' %></span>
                                        <span class="mx-2">•</span>
                                        <time><%= new Date(message.received).toLocaleString() %></time>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-1">
                                        <%= message.subject || 'No Subject' %>
                                    </h3>
                                    <p class="text-gray-600 text-sm">
                                        <%= message.summary || (message.text && message.text.body ? message.text.body.substring(0, 150) + '...' : 'No preview available') %>
                                    </p>
                                </div>
                                <div class="ml-4">
                                    <a href="/mail-testing/<%= message.id %>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        Check Email
                                    </a>
                                </div>
                            </div>
                        </li>
                    <% }) %>
                </ul>
            </div>
        </div>
    </main>
</body>
</html>