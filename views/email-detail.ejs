<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter Testing Tool - Email Detail</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold">Newsletter Testing Tool</h1>
                </div>
            </div>
        </div>
    </nav>
    
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div>
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div class="px-4 py-5 sm:px-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                <%= emailDetails.subject || 'No Subject' %>
                            </h3>
                            <div class="mt-2 max-w-xl text-sm text-gray-500">
                                <p><strong>From:</strong> <%= emailDetails.from ? emailDetails.from.name : 'Unknown' %> &lt;<%= emailDetails.from ? emailDetails.from.email : 'Unknown' %>&gt;</p>
                                <p><strong>Date:</strong> <%= new Date(emailDetails.received).toLocaleString() %></p>
                            </div>
                        </div>
                        <button onclick="window.open('/email-html/<%= messageId %>', '_blank')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Open Email HTML
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow sm:rounded-lg">
                <div class="p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Feedback</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="whitespace-pre-wrap text-sm text-gray-700"><%- geminiResponse.replace(/\n/g, '<br>') %></div>
                    </div>
                </div>
            </div>

            <div class="mt-6">
                <a href="/mail-testing" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    ← Back to Mail List
                </a>
            </div>
        </div>
    </main>
</body>
</html>