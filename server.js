const express = require('express');
const path = require('path');
const dotenv = require('dotenv');
const { GoogleGenAI } = require('@google/genai');
const cheerio = require('cheerio');

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

const API_KEY = process.env.MAILOSAUR_API_KEY;
const SERVER_ID = process.env.MAILOSAUR_SERVER;
const BASE_URL = 'https://mailosaur.com/api';
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const MODEL_ID = process.env.GEMINI_MODEL_ID || 'gemini-2.5-flash';

const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(express.static(path.join(__dirname, 'public')));

async function listMessages() {
  const response = await fetch(`${BASE_URL}/messages?server=${SERVER_ID}`, {
    headers: {
      'Authorization': `Basic ${Buffer.from(`api:${API_KEY}`).toString('base64')}`
    }
  });
  
  if (!response.ok) {
    throw new Error(`Failed to fetch messages: ${response.status}`);
  }
  
  return response.json();
}

async function getMessage(messageId) {
  const response = await fetch(`${BASE_URL}/messages/${messageId}`, {
    headers: {
      'Authorization': `Basic ${Buffer.from(`api:${API_KEY}`).toString('base64')}`
    }
  });
  
  if (!response.ok) {
    throw new Error(`Failed to fetch message: ${response.status}`);
  }
  
  return response.json();
}

async function getFinalUrlLightweight(url) {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36'
      }
    });

    const html = await response.text();

    const regex = /top\.location\s*=\s*['"](https?.*?)(?:'|")|<meta\s+http-equiv="refresh"\s+content="[^;]+;\s*url=([^"]+)"/i;
    const match = html.match(regex);

    if (match) {
      let finalUrl = match[1] || match[2];

      if (finalUrl) {
        return finalUrl.replace(/\\/g, "");
      }
    }

    return null;

  } catch (error) {
    console.error('Error fetching or parsing the URL:', error);
    return null;
  }
}

async function cleanEmailHtml(htmlContent) {
  const $ = cheerio.load(htmlContent);

  const subject = $('title').text().trim();
  const bodyText = $('body').text().replace(/\s\s+/g, ' ').trim();
  const links = [];
  $('a').each((i, element) => {
    const href = $(element).attr('href');
    if (href) {
      links.push(href);
    }
  });

  console.log('\n--- EMAIL PROOFREADING DATA ---');
  console.log('\n## 📧 Email Subject:');
  console.log(subject);
  console.log('\n## ✍️ Body Text (for content check):');
  console.log(bodyText);
  console.log('\n## 🔗 Extracted Links (for validation):');
  links.forEach(link => console.log(link));

  const $body = $('body');

  $body.find('[style*="mso-hide:all"]').remove();
  $body.find('[style*="display: none"]').remove();
  $body.find('img[width="1"], img[height="1"]').remove();

  const attributesToRemove = [
    'style', 'class', 'width', 'height', 'cellpadding', 'cellspacing',
    'border', 'align', 'valign', 'role', 'bgcolor', 'data-btn'
  ];

  $body.find('*').each((i, element) => {
    for (const attr of attributesToRemove) {
      $(element).removeAttr(attr);
    }
  });

  const cleanedBodyHtml = $body.html();

  console.log('\n🔄 Processing tracking links to get real URLs...');
  const $cleanBody = cheerio.load(cleanedBodyHtml);
  
  const linkPromises = [];
  $cleanBody('a').each((i, element) => {
    const $link = $cleanBody(element);
    const href = $link.attr('href');
    
    if (href && href.includes('r.editionsmail.heni.com/tr/')) {
      linkPromises.push(
        getFinalUrlLightweight(href).then(realUrl => {
          if (realUrl) {
            $link.attr('href', realUrl);
            console.log(`✓ Converted tracking link to: ${realUrl}`);
          }
        })
      );
    }
  });

  await Promise.all(linkPromises);
  
  $cleanBody('img').each((i, element) => {
    const $img = $cleanBody(element);
    $img.attr('src', '');
  });
  console.log('✓ Stripped all image src URLs');
  
  let previousHtml = '';
  let currentHtml = $cleanBody.html();
  let iterations = 0;
  
  while (previousHtml !== currentHtml && iterations < 20) {
    previousHtml = currentHtml;
    
    ['th', 'td', 'tr', 'tbody', 'table'].forEach(tag => {
      const regex = new RegExp(`<${tag}[^>]*>([\\s\\S]*?)<\\/${tag}>`, 'gi');
      currentHtml = currentHtml.replace(regex, '$1');
    });
    
    iterations++;
  }
  
  const $cleanBodyNoTables = cheerio.load(currentHtml);
  console.log(`✓ Removed all table structure tags (${iterations} iterations)`);
  
  const cleanedBodyHtmlWithRealLinks = $cleanBodyNoTables.html();
  
  const formattedHtml = cleanedBodyHtmlWithRealLinks
    .replace(/\s*target="_blank"/g, '')
    .replace(/></g, '>\n<')
    .replace(/(<[^>]+>)\n(<\/)/g, '$1$2')
    .replace(/\n\s*\n/g, '\n');
    
  return formattedHtml;
}

const createProofreadingPrompt = (emailData) => {
  const { content, fromName, fromEmail, receivedDate, subject, currentDate } = emailData;
  
  return `You are an expert marketing proofreader with a strong eye for detail, formatting consistency, and brand accuracy.

Review the marketing email against the following checklist and flag any errors, inconsistencies, missing elements, or deviations from HENI's formatting, brand guidelines, or technical requirements. Assume a professional, meticulous standard — nothing should be missed.

Please review the marketing email against the following criteria and flag any issues:

## Links & Technical
- **Link validation**: All links must go to heni.com domains - flag any external or non-HENI links (exception: Google Maps URLs are acceptable and social media links)
- **Image links**: Ensure all images have proper links attached (ignore empty src attributes)
- **CTA buttons**: Verify presence of clear calls-to-action (Apply Now, Buy Now, Explore, etc.)

## Artist & Drop Information
- **Artist consistency**: No inconsistencies with artist names involved in the drop
- **Drop title formatting**: Drop title must be in italics
- **Artwork titles**: All artwork titles must be italicised
- **Edition size**: Must mention if edition is open/limited by demand or has specific size limit

## Product Card Requirements
For each artwork, verify it includes:
- **Dimensions**: Consistent format "80 x 80 cm" (with spaces and 'x')
- **Medium**: Full materials description included
- **Artwork code**: Each piece has proper code (e.g., PV1, ES1-1, etc.)
- **Price**: Listed in USD format
- **Tax disclaimer**: "plus applicable taxes" included with each price

## Dates & Timing
- **Application/purchase deadline**: Clear end date included and **bolded**
- **Date/time accuracy**: All dates and times are correct and achievable (ignore year in drop close dates as context makes this clear)
- **Time zone consistency**: All time zones mentioned consistently throughout
- **Display location**: Mention of where artwork is physically on display

## Language & Formatting
- **British spelling**: Check for colour/color, centre/center, organised/organized, etc.
- **Grammar and punctuation**: Proper sentence structure and punctuation
- **HENI branding**: "HENI" always in capitals
- **Pricing errors**: Flag anything that looks unusually cheap or expensive

## Required Elements
- **Subject line**: Must match email content and messaging
- **Social media links**: Must include all official HENI social links:
  - https://discord.com/invite/HENI
  - https://www.facebook.com/HENIGroup
  - https://x.com/HENI
  - https://www.instagram.com/heni/
  - https://www.tiktok.com/@heni
  - https://www.youtube.com/HENITalks

- **Customer support**: Must include proper support links:
  - Help Centre: https://customersupport.heni.com/knowledge/[artist-name]#[drop-name]
  - Contact tickets: https://customersupport.heni.com/knowledge/kb-tickets/new
  - Standard text: "If you have any questions, please check our Customer Support Help Centre or contact us here."

- **Copyright**: Copyright date at bottom must be current year

## Content Quality
- **No placeholder text**: Flag any Lorem Ipsum or [PLACEHOLDER] text
- **Complete information**: No missing critical details or incomplete sentences
- Ignore issues with HTML bloat, spacing and nbsp characters;
- Don't worry if a full stop is missing at the end of the sentence about why they received the email
- Don't worry if there are any links where there is no visible text associated with a tag such as for customer care links. 

Don't output information about what is correct - I only need to know what the issues actually are please. I want the output to be concise and readable to determine the problems rather than what is correct.

<START EMAIL CONTENTS>
${content}
<END EMAIL CONTENTS>

Email from name: ${fromName}
Email from email: ${fromEmail}
Received date: ${receivedDate}
Subject: ${subject}
Current date: ${currentDate}`;
};

async function callGemini(emailData) {
  const prompt = createProofreadingPrompt(emailData);
  
  try {
    const response = await ai.models.generateContent({
      model: MODEL_ID,
      contents: prompt,
      config: {
        thinkingConfig: {
          thinkingBudget: 8000
        }
      }
    });
    
    const responseText = response.text;
    return responseText;
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

app.get('/', (req, res) => {
  res.redirect('/mail-testing');
});

app.get('/mail-testing', async (req, res) => {
  try {
    const messages = await listMessages();
    const latestMessages = messages.items.slice(0, 10);
    res.render('mail-listing', { messages: latestMessages });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).send('Error fetching messages');
  }
});

app.get('/mail-testing/:messageId', async (req, res) => {
  try {
    const { messageId } = req.params;
    const mailosaurResponse = await getMessage(messageId);
    
    const emailDetails = {
      from: mailosaurResponse.from && mailosaurResponse.from.length > 0 ? mailosaurResponse.from[0] : null,
      to: mailosaurResponse.to && mailosaurResponse.to.length > 0 ? mailosaurResponse.to[0] : null,
      cc: mailosaurResponse.cc,
      bcc: mailosaurResponse.bcc,
      received: mailosaurResponse.received,
      subject: mailosaurResponse.subject,
      htmlBody: mailosaurResponse.html ? mailosaurResponse.html.body : null,
      textBody: mailosaurResponse.text ? mailosaurResponse.text.body : null
    };
    
    let cleanedHtml = '';
    let geminiResponse = '';
    
    if (emailDetails.htmlBody) {
      cleanedHtml = await cleanEmailHtml(emailDetails.htmlBody);
      
      const emailData = {
        content: cleanedHtml,
        fromName: emailDetails.from ? emailDetails.from.name : 'Unknown',
        fromEmail: emailDetails.from ? emailDetails.from.email : 'Unknown',
        receivedDate: emailDetails.received,
        subject: emailDetails.subject,
        currentDate: new Date().toISOString().split('T')[0]
      };
      
      geminiResponse = await callGemini(emailData);
    }
    
    res.render('email-detail', { 
      emailDetails, 
      cleanedHtml,
      geminiResponse,
      messageId 
    });
  } catch (error) {
    console.error('Error fetching message:', error);
    res.status(500).send('Error fetching message');
  }
});

app.get('/email-html/:messageId', async (req, res) => {
  try {
    const { messageId } = req.params;
    const mailosaurResponse = await getMessage(messageId);
    
    if (mailosaurResponse.html && mailosaurResponse.html.body) {
      res.send(mailosaurResponse.html.body);
    } else {
      res.send('<p>No HTML content available</p>');
    }
  } catch (error) {
    console.error('Error fetching email HTML:', error);
    res.status(500).send('Error fetching email HTML');
  }
});

app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});