{"version": 3, "file": "pseudos.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["pseudo-selectors/pseudos.ts"], "names": [], "mappings": "AASA,yEAAyE;AACzE,MAAM,CAAC,MAAM,OAAO,GAA2B;IAC3C,KAAK,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE;QACnB,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAClC,CAAC,IAAI,EAAE,EAAE;QACL,kDAAkD;QAClD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAC1D,CAAC;IACN,CAAC;IAED,aAAa,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QACnC,IAAI,OAAO,CAAC,kBAAkB,EAAE;YAC5B,OAAO,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;SACnD;QAED,MAAM,UAAU,GAAG,OAAO;aACrB,WAAW,CAAC,IAAI,CAAC;aACjB,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QACzC,OAAO,UAAU,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IACD,YAAY,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE3C,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC3C,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAAE,MAAM;SACzC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,eAAe,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC9C,IACI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;gBAC7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,QAAQ,EAC9C;gBACE,MAAM;aACT;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,cAAc,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QACpC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC9C,IACI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;gBAC7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,QAAQ,EAC9C;gBACE,MAAM;aACT;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,cAAc,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QACpC,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvC,OAAO,OAAO;aACT,WAAW,CAAC,IAAI,CAAC;aACjB,KAAK,CACF,CAAC,OAAO,EAAE,EAAE,CACR,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC;YACrB,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;YACvB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,CAC5C,CAAC;IACV,CAAC;IACD,YAAY,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;QAClC,OAAO,OAAO;aACT,WAAW,CAAC,IAAI,CAAC;aACjB,KAAK,CACF,CAAC,OAAO,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAChE,CAAC;IACV,CAAC;CACJ,CAAC;AAEF,MAAM,UAAU,gBAAgB,CAC5B,IAA6B,EAC7B,IAAY,EACZ,SAAiC,EACjC,QAAgB;IAEhB,IAAI,SAAS,KAAK,IAAI,EAAE;QACpB,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,uBAAuB,CAAC,CAAC;SACjE;KACJ;SAAM,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,6BAA6B,CAAC,CAAC;KACvE;AACL,CAAC"}