{"version": 3, "file": "attributes.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["attributes.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAAgC;AAIhC;;;;;GAKG;AACH,IAAM,OAAO,GAAG,0BAA0B,CAAC;AAC3C,SAAS,WAAW,CAAC,KAAa;IAC9B,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;GAKG;AACH,IAAM,yBAAyB,GAAG,IAAI,GAAG,CAAC;IACtC,QAAQ;IACR,gBAAgB;IAChB,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,UAAU;IACV,OAAO;IACP,SAAS;IACT,SAAS;IACT,OAAO;IACP,KAAK;IACL,WAAW;IACX,UAAU;IACV,SAAS;IACT,MAAM;IACN,OAAO;IACP,UAAU;IACV,YAAY;IACZ,MAAM;IACN,UAAU;IACV,MAAM;IACN,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,UAAU;IACV,SAAS;IACT,QAAQ;IACR,UAAU;IACV,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,WAAW;IACX,UAAU;IACV,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,WAAW;IACX,OAAO;CACV,CAAC,CAAC;AAEH,SAAS,gBAAgB,CACrB,QAA2B,EAC3B,OAA2C;IAE3C,OAAO,OAAO,QAAQ,CAAC,UAAU,KAAK,SAAS;QAC3C,CAAC,CAAC,QAAQ,CAAC,UAAU;QACrB,CAAC,CAAC,QAAQ,CAAC,UAAU,KAAK,QAAQ;YAClC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU;YACtB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC3E,CAAC;AAED;;GAEG;AACU,QAAA,cAAc,GAOvB;IACA,MAAM,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO;QACd,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QACpB,IAAA,IAAI,GAAK,IAAI,KAAT,CAAU;QAChB,IAAA,KAAK,GAAK,IAAI,MAAT,CAAU;QAErB,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACjC,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAE5B,OAAO,UAAC,IAAI;gBACR,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnD,OAAO,CACH,IAAI,IAAI,IAAI;oBACZ,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;oBAC5B,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK;oBAC5B,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;YACN,CAAC,CAAC;SACL;QAED,OAAO,UAAC,IAAI;YACR,OAAA,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;QAA7D,CAA6D,CAAC;IACtE,CAAC;IACD,MAAM,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO;QACd,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QACpB,IAAA,IAAI,GAAK,IAAI,KAAT,CAAU;QAChB,IAAA,KAAK,GAAK,IAAI,MAAT,CAAU;QACrB,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QAEzB,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACjC,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAE5B,OAAO,SAAS,QAAQ,CAAC,IAAI;gBACzB,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnD,OAAO,CACH,IAAI,IAAI,IAAI;oBACZ,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC;oBACjD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK;oBAC3C,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;YACN,CAAC,CAAC;SACL;QAED,OAAO,SAAS,MAAM,CAAC,IAAI;YACvB,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACnD,OAAO,CACH,IAAI,IAAI,IAAI;gBACZ,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC;gBACjD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,KAAK;gBAC7B,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IACD,OAAO,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO;QACf,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QACpB,IAAA,IAAI,GAAY,IAAI,KAAhB,EAAE,KAAK,GAAK,IAAI,MAAT,CAAU;QAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAClB,OAAO,kBAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,IAAM,KAAK,GAAG,IAAI,MAAM,CACpB,mBAAY,WAAW,CAAC,KAAK,CAAC,cAAW,EACzC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAC7C,CAAC;QAEF,OAAO,SAAS,OAAO,CAAC,IAAI;YACxB,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACnD,OAAO,CACH,IAAI,IAAI,IAAI;gBACZ,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM;gBAC3B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IACD,MAAM,YAAC,IAAI,EAAE,EAAQ,EAAE,EAAW;YAAnB,IAAI,UAAA;YAAM,OAAO,aAAA;QAC5B,OAAO,UAAC,IAAI,IAAK,OAAA,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAA3C,CAA2C,CAAC;IACjE,CAAC;IACD,KAAK,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO;QACb,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QACpB,IAAA,IAAI,GAAK,IAAI,KAAT,CAAU;QAChB,IAAA,KAAK,GAAK,IAAI,MAAT,CAAU;QACrB,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QAEzB,IAAI,GAAG,KAAK,CAAC,EAAE;YACX,OAAO,kBAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACjC,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAE5B,OAAO,UAAC,IAAI;gBACR,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnD,OAAO,CACH,IAAI,IAAI,IAAI;oBACZ,IAAI,CAAC,MAAM,IAAI,GAAG;oBAClB,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK;oBAC3C,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;YACN,CAAC,CAAC;SACL;QAED,OAAO,UAAC,IAAI;;YACR,OAAA,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,0CAAE,UAAU,CAAC,KAAK,CAAC,CAAA;gBAC1D,IAAI,CAAC,IAAI,CAAC,CAAA;SAAA,CAAC;IACnB,CAAC;IACD,GAAG,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO;QACX,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QACpB,IAAA,IAAI,GAAK,IAAI,KAAT,CAAU;QAChB,IAAA,KAAK,GAAK,IAAI,MAAT,CAAU;QACrB,IAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;QAE1B,IAAI,GAAG,KAAK,CAAC,EAAE;YACX,OAAO,kBAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACjC,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAE5B,OAAO,UAAC,IAAI;;gBACR,OAAA,CAAA,MAAA,OAAO;qBACF,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,0CAC5B,MAAM,CAAC,GAAG,EACX,WAAW,EAAE,MAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA;aAAA,CAAC;SAClD;QAED,OAAO,UAAC,IAAI;;YACR,OAAA,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,0CAAE,QAAQ,CAAC,KAAK,CAAC,CAAA;gBACxD,IAAI,CAAC,IAAI,CAAC,CAAA;SAAA,CAAC;IACnB,CAAC;IACD,GAAG,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO;QACX,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QACpB,IAAA,IAAI,GAAY,IAAI,KAAhB,EAAE,KAAK,GAAK,IAAI,MAAT,CAAU;QAE7B,IAAI,KAAK,KAAK,EAAE,EAAE;YACd,OAAO,kBAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACjC,IAAM,OAAK,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;YAElD,OAAO,SAAS,KAAK,CAAC,IAAI;gBACtB,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnD,OAAO,CACH,IAAI,IAAI,IAAI;oBACZ,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM;oBAC3B,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC;oBAChB,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;YACN,CAAC,CAAC;SACL;QAED,OAAO,UAAC,IAAI;;YACR,OAAA,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,0CAAE,QAAQ,CAAC,KAAK,CAAC,CAAA;gBACxD,IAAI,CAAC,IAAI,CAAC,CAAA;SAAA,CAAC;IACnB,CAAC;IACD,GAAG,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO;QACX,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QACpB,IAAA,IAAI,GAAK,IAAI,KAAT,CAAU;QAChB,IAAA,KAAK,GAAK,IAAI,MAAT,CAAU;QAErB,IAAI,KAAK,KAAK,EAAE,EAAE;YACd,OAAO,UAAC,IAAI;gBACR,OAAA,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;YAArD,CAAqD,CAAC;SAC7D;aAAM,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACxC,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAE5B,OAAO,UAAC,IAAI;gBACR,IAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnD,OAAO,CACH,CAAC,IAAI,IAAI,IAAI;oBACT,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;oBAC5B,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;oBACjC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;YACN,CAAC,CAAC;SACL;QAED,OAAO,UAAC,IAAI;YACR,OAAA,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;QAA7D,CAA6D,CAAC;IACtE,CAAC;CACJ,CAAC"}