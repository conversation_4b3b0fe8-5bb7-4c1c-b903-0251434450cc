{"version": 3, "file": "querying.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/b3f14ecdb644827d0a7ea8f441abe2063c7ce2e7/src/", "sources": ["helpers/querying.ts"], "names": [], "mappings": ";;AAWA,0BA6CC;AAWD,0BA4CC;AAED,0CASC;AAED,4CAMC;AAhID;;;;;;;;GAQG;AACH,SAAgB,OAAO,CACnB,KAA6B,EAC7B,KAAa,EACb,OAA2C;IAEnC,IAAA,OAAO,GAAsB,OAAO,QAA7B,EAAE,KAAoB,OAAO,QAAZ,EAAf,OAAO,mBAAG,KAAK,KAAA,CAAa;IAC7C,IAAM,MAAM,GAAkB,EAAE,CAAC;IACjC,6CAA6C;IAC7C,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1B,8CAA8C;IAC9C,IAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,SAAS,CAAC;QACN,sEAAsE;QACtE,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YACvC,qDAAqD;YACrD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,MAAM,CAAC;YAClB,CAAC;YAED,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,0DAA0D;YAC1D,SAAS;QACb,CAAC;QAED,IAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YAAE,SAAS;QACnC,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE,CAAC;YAClD;;;eAGG;YACH,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAE3C,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC5B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;QACL,CAAC;IACL,CAAC;AACL,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,OAAO,CACnB,KAA6B,EAC7B,KAAa,EACb,OAA2C;IAEnC,IAAA,OAAO,GAAsB,OAAO,QAA7B,EAAE,KAAoB,OAAO,QAAZ,EAAf,OAAO,mBAAG,KAAK,KAAA,CAAa;IAC7C,6CAA6C;IAC7C,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1B,8CAA8C;IAC9C,IAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,SAAS,CAAC;QACN,sEAAsE;QACtE,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YACvC,qDAAqD;YACrD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,0DAA0D;YAC1D,SAAS;QACb,CAAC;QAED,IAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;YAAE,SAAS;QACnC,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC;QAE7B,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE,CAAC;YAClD;;;eAGG;YACH,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAE3C,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC5B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;QACL,CAAC;IACL,CAAC;AACL,CAAC;AAED,SAAgB,eAAe,CAC3B,IAAU,EACV,OAAmC;IAEnC,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3C,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC;QAAE,OAAO,EAAE,CAAC;IACpC,IAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,EAAE,CAAC;IAClE,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAED,SAAgB,gBAAgB,CAC5B,IAAiB,EACjB,OAAmC;IAEnC,IAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvC,OAAO,MAAM,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;AACnE,CAAC"}