{"name": "@google/genai", "version": "1.12.0", "description": "", "type": "module", "main": "dist/node/index.mjs", "module": "dist/web/index.mjs", "browser": "dist/web/index.mjs", "typings": "dist/genai.d.ts", "exports": {".": {"browser": {"types": "./dist/web/web.d.ts", "import": "./dist/web/index.mjs", "default": "./dist/web/index.mjs"}, "node": {"types": "./dist/node/node.d.ts", "import": "./dist/node/index.mjs", "require": "./dist/node/index.cjs", "default": "./dist/node/index.mjs"}, "types": "./dist/genai.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}, "./web": {"types": "./dist/web/web.d.ts", "import": "./dist/web/index.mjs", "default": "./dist/web/index.mjs"}, "./node": {"types": "./dist/node/node.d.ts", "import": "./dist/node/index.mjs", "default": "./dist/node/index.mjs"}}, "scripts": {"prepare": "npm run build-prod", "build": "rollup -c && api-extractor run --local --verbose && api-extractor run -c api-extractor.node.json --local --verbose&& api-extractor run -c api-extractor.web.json --local --verbose && node scripts/ignore_missing_mcp_dep.js", "build-prod": "rollup -c && api-extractor run --verbose && api-extractor run -c api-extractor.node.json --verbose && api-extractor run -c api-extractor.web.json --verbose && node scripts/ignore_missing_mcp_dep.js", "unit-test": "tsc && jasmine dist/test/unit/**/*_test.js dist/test/unit/*_test.js", "system-test": "tsc && jasmine dist/test/system/**/*_test.js", "test-server-tests": "tsc && GOOGLE_CLOUD_PROJECT=googcloudproj GOOGLE_CLOUD_LOCATION=googcloudloc jasmine dist/test/system/node/*_test.js -- --test-server", "test-server-tests:record": "tsc && jasmine --fail-fast dist/test/system/node/*_test.js -- --test-server --record", "docs": "typedoc && node --loader ts-node/esm scripts/add_docsite_license_headers.ts", "pages-main": "node --loader ts-node/esm scripts/generate_pages.ts main", "pages-release": "node --loader ts-node/esm scripts/generate_pages.ts release", "format": "prettier '**/*.ts' '**/*.mjs' '**/*.json' --write", "lint": "eslint '**/*.ts'", "lint-fix": "eslint --fix '**/*.ts'"}, "engines": {"node": ">=20.0.0"}, "files": ["dist/genai.d.ts", "dist/index.mjs", "dist/index.cjs", "dist/index.mjs.map", "dist/node/index.mjs", "dist/node/index.cjs", "dist/node/index.mjs.map", "dist/node/node.d.ts", "dist/web/index.mjs", "dist/web/index.mjs.map", "dist/web/web.d.ts", "node/package.json", "web/package.json"], "devDependencies": {"@eslint/js": "9.20.0", "@microsoft/api-extractor": "^7.50.1", "@rollup/plugin-json": "^6.1.0", "@types/jasmine": "^5.1.2", "@types/node": "^20.9.0", "@types/unist": "^3.0.3", "@types/ws": "^8.5.14", "eslint": "8.57.0", "gts": "^5.2.0", "jasmine": "^5.5.0", "jasmine-reporters": "^2.4.0", "nyc": "^17.1.0", "prettier": "3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "rollup-plugin-typescript2": "^0.36.0", "test-server-sdk": "^0.2.6", "ts-node": "^10.9.2", "tslib": "^2.8.1", "tsx": "^4.19.4", "typedoc": "^0.27.0", "typescript": "~5.2.0", "typescript-eslint": "8.24.1", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.4"}, "dependencies": {"google-auth-library": "^9.14.2", "ws": "^8.18.0"}, "peerDependencies": {"@modelcontextprotocol/sdk": "^1.11.0"}, "peerDependenciesMeta": {"@modelcontextprotocol/sdk": {"optional": true}}, "repository": {"type": "git", "url": "https://github.com/googleapis/js-genai.git"}, "bugs": {"url": "https://github.com/googleapis/js-genai/issues"}, "homepage": "https://github.com/googleapis/js-genai#readme", "author": "", "license": "Apache-2.0"}