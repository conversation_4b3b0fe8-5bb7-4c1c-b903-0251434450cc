{"version": 3, "file": "WritableStream.js", "sourceRoot": "", "sources": ["../../src/WritableStream.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAoC,MAAM,aAAa,CAAC;AACvE;;;GAGG;AACH,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACvC,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAEpD,2GAA2G;AAC3G,SAAS,QAAQ,CAAC,MAAuB,EAAE,QAAgB;IACvD,OAAO,QAAQ,KAAK,QAAQ,CAAC;AACjC,CAAC;AAED;;;;GAIG;AACH,MAAM,OAAO,cAAe,SAAQ,QAAQ;IAIxC,YAAY,GAAqB,EAAE,OAAuB;QACtD,KAAK,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;QAHnB,aAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QAI5C,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAEQ,MAAM,CACX,KAAsB,EACtB,QAAgB,EAChB,QAAoB;QAEpB,IAAI,CAAC,OAAO,CAAC,KAAK,CACd,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CACjE,CAAC;QACF,QAAQ,EAAE,CAAC;IACf,CAAC;IAEQ,MAAM,CAAC,QAAoB;QAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;QACtC,QAAQ,EAAE,CAAC;IACf,CAAC;CACJ"}